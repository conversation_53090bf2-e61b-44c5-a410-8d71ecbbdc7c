<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class UserSubscription extends Model
{
//    use HasFactory;

    protected $fillable = [
        'user_id',
        'subscription_id',
        'is_trial',
        'trial_expires_at',
        'expires_at',
    ];

    // Relationship with User
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // Relationship with Subscription Plan
    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }

    // Check if the trial is active
    public function isTrialActive()
    {
        return $this->is_trial && now()->lessThan($this->trial_expires_at);
    }
}
