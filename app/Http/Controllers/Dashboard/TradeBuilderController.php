<?php

namespace App\Http\Controllers\Dashboard;

use App\Models\PortfolioFieldDefinition;
use App\Models\TradeFieldDefinition;
use App\Models\TransactionFieldDefinition;
use Inertia\Inertia;
use Illuminate\Http\Request;
use App\Services\TradeBuilderService;
use App\Services\FormulaEvaluatorService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\File;

class TradeBuilderController extends Controller
{
    public function __construct(protected TradeBuilderService $tradeBuilderService, protected FormulaEvaluatorService $evaluator)
    {
    }

    /**
     * fetch a trade default entry and exit form fields.
     */
    public function index(): JsonResponse
    {
        return response()->json(
            $this->tradeBuilderService->getEntryAndExitFields()
        );
    }

    public function store(Request $request)
    {
        $user = auth()->user();

        $trade = $user->trades()->create([
            'draft_title'  => $request->input('title', 'Untitled Draft'),
            'is_published' => true,
        ]);

        foreach ($request->input('formData') as $formKey => $form) {
            $type  = str_contains($formKey, 'entry') ? 'entry' : 'exit';
            $index = (int)filter_var($formKey, FILTER_SANITIZE_NUMBER_INT);

            $tradeForm = $trade->forms()->create([
                'type'  => $type,
                'index' => $index,
            ]);

            foreach (['overview', 'projection', 'outcome'] as $section) {
                $sectionData = $form[$section] ?? [];
                $tradeForm->sections()->create([
                    'section' => $section,
                    'data'    => $sectionData,
                ]);
            }
        }

        return response()->json(['message' => 'Trade saved!']);
    }

    public function calculateFormulas(Request $request)
    {
        $inputs            = $request->input('inputs', []);
        $locked            = $request->input('locked', []);
        $formulaModeInputs = $request->input('formulaModeInputs', []);

        $lockedFields = array_map('strtoupper', array_keys(array_filter($locked, fn($value) => $value === true)));
        $changedField = strtoupper($request->input('changedField', ''));

        $inputs = collect($inputs)
            ->mapWithKeys(fn($v, $k) => [strtoupper($k) => $v])
            ->toArray();

        $formulaModeInputs = collect($formulaModeInputs)
            ->mapWithKeys(fn($v, $k) => [strtoupper($k) => $v])
            ->toArray();

        $jsonFile    = public_path('TradeReply_Formulas.json');
        $formulaData = json_decode(file_get_contents($jsonFile), true) ?? [];

        $updatedFields  = [];
        $iterationCount = 0;
        $maxIterations  = 15;

        do {
            $newUpdates = [];

            foreach ($formulaData as $formulaItem) {
                foreach (['TRADE', 'TRANSACTION', 'PORTFOLIO'] as $scopeKey) {
                    $scope = $formulaItem['SCOPES'][$scopeKey] ?? null;
                    if (!$scope) continue;

                    $targetField = strtoupper($scope['DATABASE FIELD'] ?? '');
                    $formula     = $scope['FORMULA'] ?? null;
                    if (!$targetField || !$formula) continue;

                    $dependentFields = $this->extractFormulaDependencies($formula);

                    if (in_array($targetField, $lockedFields, true)) {
                        continue;
                    }

                    if (isset($formulaModeInputs[$targetField]) && !$formulaModeInputs[$targetField]) {
                        continue;
                    }

                    if (
                        in_array($changedField, $dependentFields, true) ||
                        array_intersect(array_keys($updatedFields), $dependentFields)
                    ) {
                        $calculated = $this->evaluateFallbackFormula($formula, $inputs);
                        if (!is_null($calculated)) {
                            $newUpdates[$targetField] = $calculated;
                            $inputs[$targetField]     = $calculated;
                        }
                    }
                }
            }

            $updatedFields = array_merge($updatedFields, $newUpdates);

            $iterationCount++;
        } while (!empty($newUpdates) && $iterationCount < $maxIterations);

        return response()->json([
            'success'    => true,
            'calculated' => $updatedFields,
        ]);
    }

    private function extractFormulaDependencies(array $formula): array
    {
        $dependencies = [];

        for ($i = 1; $i <= 5; $i++) {
            $flag  = $formula["f{$i}"] ?? 0;
            $value = $formula["f{$i}v"] ?? null;

            if ($flag && $value) {
                $dependencies = array_merge($dependencies, $this->scanFieldsFromFormula($value));
            }
        }

        return array_unique(array_map('strtoupper', array_filter($dependencies, 'is_string')));
    }

    private function scanFieldsFromFormula($node): array
    {
        $fields = [];

        if (is_string($node)) {
            $fields[] = $node;
        }

        if (is_array($node)) {
            if (!empty($node['field'])) {
                $fields[] = $node['field'];
            }

            if (isset($node['condition']) && is_string($node['condition'])) {
                $fields[] = $node['condition'];
            }

            if (isset($node['value']) && is_string($node['value'])) {
                $fields[] = $node['value'];
            }

            if (!empty($node['condition']['field'])) {
                $fields[] = $node['condition']['field'];
            }

            if (!empty($node['fields']) && is_array($node['fields'])) {
                foreach ($node['fields'] as $child) {
                    $fields = array_merge($fields, $this->scanFieldsFromFormula($child));
                }
            }

            if (!empty($node['true_case'])) {
                $fields = array_merge($fields, $this->scanFieldsFromFormula($node['true_case']));
            }

            if (!empty($node['false_case'])) {
                $fields = array_merge($fields, $this->scanFieldsFromFormula($node['false_case']));
            }
        }

        return $fields;
    }

    private function evaluateFallbackFormula(array $formula, array $inputs)
    {
        for ($i = 1; $i <= 5; $i++) {
            if (($formula["f$i"] ?? 0) == 1 && isset($formula["f{$i}v"])) {
                $dependencies = $this->extractFormulaDependencies($formula["f{$i}v"]);

                $hasAllDependencies = true;
                foreach ($dependencies as $depField) {
                    $upperDep = strtoupper($depField);
                    if (!isset($inputs[$upperDep]) || $inputs[$upperDep] === '' || $inputs[$upperDep] === null) {
                        $hasAllDependencies = false;
                        break;
                    }
                }

                if ($hasAllDependencies) {
                    return $this->evaluateFormula($formula["f{$i}v"], $inputs);
                }
            }
        }
        return null;
    }

    private function evaluateFormula(array|string $formula, array $inputs)
    {
        if (is_string($formula)) {
            $upper = strtoupper($formula);
            return array_key_exists($upper, $inputs) ? $inputs[$upper] : $formula;
        }

        $op     = strtoupper($formula['operation'] ?? '');
        $fields = $formula['fields'] ?? [];

        switch ($op) {
            case 'ADD':
                $values = array_map(function ($f) use ($inputs) {
                    $val = $this->evaluateFormula($f, $inputs);
                    return is_numeric($val) ? floatval($val) : 0;
                }, $fields);

                return array_sum($values);

            case 'SUBTRACT':
                $a = $this->evaluateFormula($fields[0], $inputs);
                $b = $this->evaluateFormula($fields[1], $inputs);
                return (is_numeric($a) ? floatval($a) : 0) - (is_numeric($b) ? floatval($b) : 0);

            case 'MULTIPLY':
                return array_product(array_map(function ($f) use ($inputs) {
                    $val = $this->evaluateFormula($f, $inputs);
                    return is_numeric($val) ? floatval($val) : 1;
                }, $fields));

            case 'DIVIDE':
                $numerator   = $this->evaluateFormula($fields[0], $inputs);
                $denominator = $this->evaluateFormula($fields[1], $inputs);
                $num         = is_numeric($numerator) ? (float)$numerator : 0;
                $den         = is_numeric($denominator) ? (float)$denominator : 0;
                return $den == 0 ? 0 : $num / $den;

            case 'AVERAGE':
                $values        = array_map(fn($f) => $this->evaluateFormula($f, $inputs), $fields);
                $numericValues = array_filter($values, fn($v) => is_numeric($v));
                return count($numericValues) ? array_sum($numericValues) / count($numericValues) : null;

            case 'ABS':
                return abs($this->evaluateFormula($fields[0], $inputs));

            case 'REFERENCE':
                $field = strtoupper($formula['field'] ?? '');
                return $inputs[$field] ?? null;

            case 'WHEN_SET':
                $conditionKey = strtoupper($formula['condition'] ?? '');
                $valueKey     = strtoupper($formula['value'] ?? '');
                if (!empty($inputs[$conditionKey])) {
                    return $inputs[$valueKey] ?? null;
                }
                return null;

            case 'IF':
                $cond              = $formula['condition'];
                $evaluateCondition = function ($cond) use (&$evaluateCondition, $inputs) {
                    $op = strtoupper($cond['operation'] ?? '');

                    if ($op === 'OR' || $op === 'AND') {
                        $results = array_map(fn($c) => $evaluateCondition($c), $cond['conditions'] ?? []);
                        return $op === 'OR' ? in_array(true, $results, true) : !in_array(false, $results, true);
                    }

                    $field = isset($cond['field']) && is_string($cond['field']) ? strtoupper($cond['field']) : '';
                    $val   = $inputs[$field] ?? null;

                    return match ($op) {
                        '=', '==' => $val == $cond['value'],
                        '!=', '<>' => $val != $cond['value'],
                        '>' => $val > $cond['value'],
                        '>=' => $val >= $cond['value'],
                        '<' => $val < $cond['value'],
                        '<=' => $val <= $cond['value'],
                        'IN' => in_array($val, $cond['values'] ?? []),
                        'IS_NULL' => is_null($val) || $val === '',
                        default => false,
                    };
                };

                $match = $evaluateCondition($cond);
                return $this->evaluateFormula($match ? $formula['true_case'] : $formula['false_case'], $inputs);

            case 'DATE_FORMAT':
                $field     = strtoupper($formula['field']);
                $timestamp = strtotime($inputs[$field] ?? '');
                return $timestamp ? date($formula['format'] ?? 'Y-m-d', $timestamp) : null;

            case 'YEAR':
                return date('Y', strtotime($inputs[strtoupper($formula['field'])] ?? ''));

            case 'WEEK':
                return date('W', strtotime($inputs[strtoupper($formula['field'])] ?? ''));

            case 'TIMESTAMPDIFF':
                $unit  = $formula['unit'] ?? 'day';
                $start = strtotime($this->evaluateFormula($formula['field1'], $inputs));
                $end   = strtotime($this->evaluateFormula($formula['field2'], $inputs));
                if (!$start || !$end) return null;
                return match ($unit) {
                    'hour' => round(($end - $start) / 3600),
                    'day' => round(($end - $start) / 86400),
                    'month' => date('n', $end) - date('n', $start) + 12 * (date('Y', $end) - date('Y', $start)),
                    'year' => date('Y', $end) - date('Y', $start),
                    default => null,
                };

            default:
                return null;
        }
    }

    /**
     * Fetch all dynamic fields for Transaction, Trade, and Portfolio.
     */
    public function fetchAllFields(): JsonResponse
    {
        $formulas = $this->tradeBuilderService->getTradeReplayFormulas();

        $tradeFields       = [];
        $portfolioFields   = [];
        $transactionFields = [];

        foreach ($formulas as $item) {
            $scopes = $item['SCOPES'] ?? [];

            if (isset($scopes['TRANSACTION']['DATABASE FIELD'])) {
                $transactionFields[] = $scopes['TRANSACTION']['DATABASE FIELD'];
            }

            if (isset($scopes['TRADE']['DATABASE FIELD'])) {
                $tradeFields[] = $scopes['TRADE']['DATABASE FIELD'];
            }

            if (isset($scopes['PORTFOLIO']['DATABASE FIELD'])) {
                $portfolioFields[] = $scopes['PORTFOLIO']['DATABASE FIELD'];
            }
        }

        $transactions = TransactionFieldDefinition::query()
            ->join('field_definitions', 'transaction_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('transaction_field_definitions.database_field', $transactionFields)
            ->whereNotIn('transaction_field_definitions.database_field', ['transaction_manual_deposit', 'transaction_manual_deposit_type', 'transaction_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'transaction_field_definitions.database_field',
                'transaction_field_definitions.summary',
                'transaction_field_definitions.account_field',
                'transaction_field_definitions.account_field_value'
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        $trades = TradeFieldDefinition::query()
            ->join('field_definitions', 'trade_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('trade_field_definitions.database_field', $tradeFields)
            ->whereNotIn('trade_field_definitions.database_field', ['trade_manual_deposit', 'trade_manual_deposit_type', 'trade_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'trade_field_definitions.database_field',
                'trade_field_definitions.summary',
                'trade_field_definitions.id as id',
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        $portfolios = PortfolioFieldDefinition::query()
            ->with(['userFieldValue' => function ($query) {
                $query->where('user_id', auth()->id());
            }])
            ->join('field_definitions', 'portfolio_field_definitions.field_definition_id', '=', 'field_definitions.id')
            ->whereIn('portfolio_field_definitions.database_field', $portfolioFields)
            ->whereNotIn('portfolio_field_definitions.database_field', ['portfolio_manual_deposit', 'portfolio_manual_deposit_type', 'portfolio_withdrawal'])
            ->get([
                'field_definitions.field_name',
                'field_definitions.metric_dimension',
                'field_definitions.expected_values',
                'portfolio_field_definitions.database_field',
                'portfolio_field_definitions.summary',
                'portfolio_field_definitions.account_field',
                'portfolio_field_definitions.account_field_value',
                'portfolio_field_definitions.id as id',
            ])
            ->map(function ($item) use ($formulas) {
                $arr                    = $item->toArray();
                $arr['portfolioValue']  = $item->userFieldValue->value ?? null;
                $arr['expected_values'] = !empty($arr['expected_values'])
                    ? array_map('trim', explode(',', $arr['expected_values']))
                    : [];
                return $this->tradeBuilderService->applyIsEditable($arr, $formulas);
            })
            ->toArray();

        return response()->json([
            'transactions' => $transactions,
            'trades'       => $trades,
            'portfolios'   => $portfolios
        ]);
    }
}
