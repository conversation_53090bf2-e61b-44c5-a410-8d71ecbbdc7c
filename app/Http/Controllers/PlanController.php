<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Stripe\Stripe;
use Stripe\Customer;
use App\Models\User;
use Illuminate\Http\Request;
use Stripe\Checkout\Session;
use Illuminate\Support\Facades\Log;

class PlanController extends Controller
{
    public function index()
    {
        $plans = Plan::with('rules')
            ->where('billing_type', '!=', 'free')
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $plans,
        ]);
    }

    public function createCheckoutSession(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        $request->validate([
           'plan_id' => 'required|exists:plans,id'
        ]);

        $user = auth()->user();

        $plan = Plan::find($request->plan_id);

        if (!$plan || !$plan->stripe_price_id) {
            return response()->json(['erro  r' => 'Invalid plan or missing Stripe price ID'], 400);
        }

        try {
            $customerId = $this->getOrCreateCustomer($user);
            $sessionData = [
                'payment_method_types' => ['card'],
                'mode'                 => 'subscription',
                'customer'             => $customerId,
                'line_items'           => [[
                    'price'    => $plan->stripe_price_id,
                    'quantity' => 1,
                ]],
                'success_url' => env('FRONTEND_URL', 'http://localhost:3000') . '/pricing?session_id={CHECKOUT_SESSION_ID}',
                'cancel_url'  => env('FRONTEND_URL', 'http://localhost:3000') . '/pricing',
            ];

            $session = Session::create($sessionData);

            return response()->json([
                'status' => 'success',
                'session_id' => $session->id,
            ], status: 200);
        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe checkout error: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to create checkout session: ' . $e->getMessage()], 500);
        } catch (\Exception $e) {
            Log::error('General error: ' . $e->getMessage());
            return response()->json(['error' => 'An unexpected error occurred'], 500);
        }
    }

    /**
     * @param User $user
     * @return string
     * @throws \Stripe\Exception\ApiErrorException
     */
    public function getOrCreateCustomer(User $user): string
    {
        if ($user->stripe_customer_id) {
            return $user->stripe_customer_id;
        }

        $customer = Customer::create([
            'email' => $user->email,
            'name'  => $user->name,
        ]);

        $user->stripe_customer_id = $customer->id;
        $user->save();

        return $customer->id;
    }
}
