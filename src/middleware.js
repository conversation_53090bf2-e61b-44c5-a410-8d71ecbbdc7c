import { NextResponse } from 'next/server';

export function middleware(req) {
  const token = req.cookies.get('authToken');
  const authFlow = req.cookies.get('auth_flow_verified');

  const ref = req.nextUrl.searchParams.get("ref");
  const pathname = req.nextUrl.pathname;

  const subscriptionIdRaw = req.cookies.get('subscription_id');
  const subscriptionId = typeof subscriptionIdRaw === 'string'
    ? subscriptionIdRaw.trim()
    : subscriptionIdRaw?.value?.trim(); // fallback for older middleware structure

  const isFreeUser = subscriptionId === '1';
  const isLoggedIn = !!token;

  const firstLogin = req.cookies.get('first_login')?.value || req.cookies.get('first_login');
  const returningLogin = req.cookies.get('returning_login')?.value || req.cookies.get('returning_login');


  // const protectedRoutes = ['/user'];
  const authRoutes = ['/login', '/signup', '/forget-password', '/verify-email', '/home']

   // ✅ Routes requiring auth (prefix-based for scalability)
   const protectedPrefixes = [
    '/user',
    '/account',
    '/dashboard',
    '/super-admin',
    '/not-found',
  ];

  // ✅ Flow-specific protected pages
  const flowProtectedRoutes = ['/change-password'];



  // if (protectedRoutes.some((route) => req.nextUrl.pathname.startsWith(route))) {
  //   if (!token) {
  //     return NextResponse.redirect(new URL('/login', req.url));
  //   }
  // }
  // if (authRoutes.some((route) => req.nextUrl.pathname.startsWith(route))) {
  //   if (token) {
  //     return NextResponse.redirect(new URL('/user/dashboard', req.url));
  //   }
  // }

  // ✅ Block access to protected pages if not logged in
  // if (protectedRoutes.some((route) => pathname.startsWith(route))) {
  //   if (!token) {
  //     return NextResponse.redirect(new URL('/login', req.url));
  //   }
  // }

  // ✅ Block access to login/signup if already logged in
  if (authRoutes.some((route) => pathname.startsWith(route))) {
    if (token) {
      return NextResponse.redirect(new URL('/user/dashboard', req.url));
    }
  }

  // ✅ Prevent unauthorized SSR of flow pages
  if (flowProtectedRoutes.includes(pathname)) {
    if (!token && !authFlow) {
      return NextResponse.redirect(new URL('/login', req.url), 302);
    }
  }
  // ✅ Require login for protected routes
  if (protectedPrefixes.some((prefix) => pathname.startsWith(prefix))) {
    if (!isLoggedIn) {
      return NextResponse.redirect(new URL('/login', req.url));
    }
  }


  // ✅ Handle first login (signup)
  if (firstLogin && isLoggedIn) {
    const response = isFreeUser
      ? NextResponse.redirect(new URL('/pricing?source=signup_free_login_upgrade&feature=buy_trial', req.url))
      : NextResponse.redirect(new URL('/dashboard?source=signup_member_login_default', req.url));

    response.cookies.set('first_login', '', { maxAge: -1 });
    return response;
  }

  // ✅ Handle returning login
  if (returningLogin && isLoggedIn) {
    const response = isFreeUser
      ? NextResponse.redirect(new URL('/pricing?source=free_login_upgrade&feature=buy_trial', req.url))
      : NextResponse.redirect(new URL('/dashboard?source=member_login_default', req.url));

    response.cookies.set('returning_login', '', { maxAge: -1 });
    return response;
  }


  if (pathname === '/pricing' && isLoggedIn) {
    const hasSource = req.nextUrl.searchParams.get("source");
    if (!hasSource) {
      let source = 'default_pricing';

      if (ref === 'header') {
        source = 'header_menu_pricing';
      } else if (ref === 'footer') {
        source = 'footer_menu_pricing';
      } else if (ref === 'header_upgrade') {
        source = 'header_upgrade_button';
      }

      const prefix = isFreeUser ? 'free' : 'member';
      const redirectUrl = `/pricing?source=${prefix}_${source}&feature=buy_trial`;

      return NextResponse.redirect(new URL(redirectUrl, req.url), 302);
    }
  }
  return NextResponse.next();
}

export const config = {
  matcher: ['/admin/:path*',
    '/user/:path*',
    '/pricing',
    '/change-password',
    '/account/:path*',
    '/dashboard/:path*',
    '/super-admin/:path*',
    '/not-found',],
};
// export const config = {