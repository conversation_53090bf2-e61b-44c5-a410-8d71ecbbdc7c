export function maskEmail(email) {
    if (!email) return "";

    const parts = email.split("@");
    if (parts.length !== 2) return email;

    const username = parts[0];
    const domain = parts[1];

    if (username.length <= 2) {
        return username[0] + "*" + "@" + domain;
    }

    const firstChar = username[0];
    const lastChar = username[username.length - 1];
    const masked = "*".repeat(username.length - 2);

    return first<PERSON>har + masked + lastChar + "@" + domain;
}
