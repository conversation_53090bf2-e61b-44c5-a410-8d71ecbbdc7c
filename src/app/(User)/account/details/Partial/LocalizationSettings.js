'use client';
import React, { useMemo, useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { EditIconSvg, SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import CustomDropdown from '@/Components/common/CustumDropdown';
import { getTimezonesForDropdown } from '@/utils/getTimezonesForDropdown';
import CommonTooltip from '@/Components/UI/CommonTooltip';

export default function LocalizationSettings() {
    const timezoneOptions = useMemo(() => getTimezonesForDropdown(), []);

    const languages = [
        { label: 'English', code: 'en' },
        { label: 'Urdu', code: 'ur' },
        { label: 'Arabic', code: 'ar' },
        { label: 'French', code: 'fr' },
        { label: 'Spanish', code: 'es' },
        { label: 'German', code: 'de' },
        { label: 'Chinese (Simplified)', code: 'zh-CN' },
        { label: 'Chinese (Traditional)', code: 'zh-TW' },
        { label: 'Japanese', code: 'ja' },
        { label: 'Korean', code: 'ko' },
        { label: 'Hindi', code: 'hi' },
        { label: 'Russian', code: 'ru' },
        { label: 'Italian', code: 'it' },
        { label: 'Portuguese', code: 'pt' },
        { label: 'Turkish', code: 'tr' },
        { label: 'Persian (Farsi)', code: 'fa' },
        { label: 'Bengali', code: 'bn' },
        { label: 'Punjabi', code: 'pa' },
        { label: 'Dutch', code: 'nl' },
        { label: 'Greek', code: 'el' },
    ];

    const currencies = [
        { label: 'USD', value: 'USD' },
        { label: 'EUR', value: 'EUR' },
        { label: 'JPY', value: 'JPY' },
        { label: 'GBP', value: 'GBP' },
        { label: 'AUD', value: 'AUD' },
        { label: 'CAD', value: 'CAD' },
        { label: 'CHF', value: 'CHF' },
        { label: 'CNY', value: 'CNY' },
        { label: 'HKD', value: 'HKD' },
        { label: 'NZD', value: 'NZD' },
    ];

    const numberFormatOptions = [
        { label: 'Dot Separator', value: 'en-US' },
        { label: 'Comma Separator', value: 'de-DE' },
    ];

    const [isEditing, setIsEditing] = useState(false);

    const [savedLanguage, setSavedLanguage] = useState('English');
    const [savedTimezone, setSavedTimezone] = useState('-4 UTC (New York)');
    const [savedCurrency, setSavedCurrency] = useState('USD');
    const [savedNumberFormat, setSavedNumberFormat] = useState('en-US');

    const [tempLanguage, setTempLanguage] = useState(savedLanguage);
    const [tempTimezone, setTempTimezone] = useState(savedTimezone);
    const [tempCurrency, setTempCurrency] = useState(savedCurrency);
    const [tempNumberFormat, setTempNumberFormat] = useState(savedNumberFormat);

    const handleEditClick = () => {
        setIsEditing(true);
    };

    const handleCancel = () => {
        setTempLanguage(savedLanguage);
        setTempTimezone(savedTimezone);
        setTempCurrency(savedCurrency);
        setTempNumberFormat(savedNumberFormat);
        setIsEditing(false);
    };

    const handleSave = () => {
        setSavedLanguage(tempLanguage);
        setSavedTimezone(tempTimezone);
        setSavedCurrency(tempCurrency);
        setSavedNumberFormat(tempNumberFormat);
        setIsEditing(false);
    };

    return (
        <Col lg={12} xs={12} className="mb-3 mb-lg-4">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader">
                    <div className="common_blackcard_innerheader_content">
                        <h6>Localization Settings</h6>
                    </div>
                    <div className="common_blackcard_innerheader_icon">
                        {!isEditing && (
                            <button className="d-flex align-items-center" onClick={handleEditClick}>
                                <EditIconSvg />
                                <span className="ms-2">Update</span>
                            </button>
                        )}
                    </div>
                </div>
                <div className="common_blackcard_innerbody">
                    <div className="account_card_list">
                        <ul>
                            {/* Language */}
                            <li>
                                <Col xs={12} md={3}>
                                    <span>Language</span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        options={languages}
                                                        defaultValue={tempLanguage}
                                                        onSelect={(val) => setTempLanguage(val.label)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{savedLanguage || 'Not set'}</span>
                                    )}
                                </Col>
                            </li>
                            {/* Timezone */}
                            <li>
                                <Col xs={12} md={3}>
                                    <span>Timezone</span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        options={timezoneOptions}
                                                        defaultValue={tempTimezone}
                                                        onSelect={(val) => setTempTimezone(val.label)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{savedTimezone || 'Not set'}</span>
                                    )}
                                </Col>
                            </li>
                            {/* Currency */}
                            <li>
                                <Col xs={12} md={3}>
                                    <span>Currency</span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        options={currencies}
                                                        defaultValue={tempCurrency}
                                                        onSelect={(val) => setTempCurrency(val.label)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{savedCurrency || 'Not set'}</span>
                                    )}
                                </Col>
                            </li>
                            {/* Number Format Display */}
                            <li>
                                <Col xs={12} md={3}>
                                    <div className='d-flex align-items-center gap-1'>
                                        <span className='pe-0'>Number Format Display</span>
                                        <CommonTooltip
                                            className="CustomTooltip"
                                            content={
                                                <>
                                                    <p className='mb-2'>
                                                        Choose how numbers—such as prices, metrics, and marketplace values—are displayed across your account. This setting controls <span className='fw-800 width-autofit pe-0'>visual formatting only</span>, not how numbers are calculated or stored.
                                                    </p>
                                                    <p className='mb-1'>• <span className='fw-800 width-autofit pe-0'>Dot Separator:</span> uses commas for thousands and a dot for decimals</p>
                                                    <p className='mb-2'>• <span className='fw-800 width-autofit pe-0'>Comma Separator:</span> uses dots for thousands and a comma for decimals</p>
                                                    <p className='mb-2'>
                                                        This setting applies globally to all account-level and marketplace pages. Individual Trade Accounts may define their own display preference, but it only affects how numbers appear <span className='fw-800 width-autofit pe-0'>within the dashboard experience.</span>
                                                    </p>
                                                    <p className='mb-2'>
                                                        When entering numbers, use only digits and the correct decimal separator. Thousands separators are added automatically and cannot be typed manually.
                                                    </p>
                                                </>
                                            }
                                            position="top-right"
                                        >
                                            <SolidInfoIcon />
                                        </CommonTooltip>
                                    </div>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        options={numberFormatOptions}
                                                        defaultValue={tempNumberFormat}
                                                        onSelect={(val) => setTempNumberFormat(val.label)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{savedNumberFormat || 'Not set'}</span>

                                    )}
                                </Col>
                            </li>
                        </ul>

                        {isEditing && (
                            <div className="account_card_list_btns">
                                <button className="btn-style" onClick={handleSave}>
                                    Save
                                </button>
                                <button className="btn-style gray-btn" onClick={handleCancel}>
                                    Cancel
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Col >
    );
}
