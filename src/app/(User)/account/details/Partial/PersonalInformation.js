'use client';
import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import { EditIconSvg, WhiteDownArrow, SearchIcons } from '@/assets/svgIcons/SvgIcon';
import TextInput from '@/Components/UI/TextInput';
import countries from 'world-countries';
import CustomDropdown from '@/Components/common/CustumDropdown';

export default function PersonalInformation() {
    const [isEditing, setIsEditing] = useState(false);
    const [firstName, setFirstName] = useState('Aaron ');
    const [lastName, setLastName] = useState('McCloud');
    const [country, setCountry] = useState('');

    const [tempFirstName, setTempFirstName] = useState(firstName);
    const [tempLastName, setTempLastName] = useState(lastName);
    const [selectedCountry, setSelectedCountry] = useState(country);

    const selectCountry = (country) => {
        console.log('Selected:', country);
        setSelectedCountry(country.name.common);
    };

    const handleEdit = () => {
        setTempFirstName(firstName);
        setTempLastName(lastName);
        setSelectedCountry(country);
        setIsEditing(true);
    };

    const handleSave = () => {
        setFirstName(tempFirstName);
        setLastName(tempLastName);
        setCountry(selectedCountry);
        setIsEditing(false);
    };

    const handleCancel = () => {
        setIsEditing(false);
        setTempFirstName(firstName);
        setTempLastName(lastName);
        setSelectedCountry(country);
    };

    const maskName = (name) => {
        if (!name) return '';
        return name[0] + '*'.repeat(name.length - 1);
    };

    return (
        <Col lg={12} xs={12} className="mb-4 mb-lg-4">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader">
                    <div className="common_blackcard_innerheader_content">
                        <h6>Personal Information</h6>
                    </div>
                    <div className="common_blackcard_innerheader_icon">
                        <button className="d-flex align-items-center" onClick={handleEdit}>
                            <EditIconSvg />
                            <span className="ms-2">Update</span>
                        </button>
                    </div>
                </div>
                <div className="common_blackcard_innerbody">
                    <div className="account_card_list">
                        <ul>
                            <li>
                                <Col xs={12} md={3}>
                                    <span className="label">Name </span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row className="w-100">
                                                <Col xs={6}>
                                                    <TextInput
                                                        className="mb-0"
                                                        placeholder="First Name"
                                                        value={tempFirstName}
                                                        onChange={(e) => setTempFirstName(e.target.value)}
                                                    />
                                                </Col>
                                                <Col xs={6}>
                                                    <TextInput
                                                        className="mb-0"
                                                        placeholder="Last Name"
                                                        value={tempLastName}
                                                        onChange={(e) => setTempLastName(e.target.value)}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>
                                            {maskName(firstName)} {maskName(lastName)}
                                        </span>
                                    )}
                                </Col>
                            </li>

                            <li>
                                <Col xs={12} md={3}>
                                    <span >Country/Region </span>
                                </Col>
                                <Col xs={12} md={9}>
                                    {isEditing ? (
                                        <div className="account_card_list_form">
                                            <Row>
                                                <Col xs={12}>
                                                    <CustomDropdown
                                                        options={countries.map((c) => ({ label: c.name.common, ...c }))}
                                                        defaultValue="Select Country"
                                                        onSelect={selectCountry}
                                                    />
                                                </Col>
                                            </Row>
                                        </div>
                                    ) : (
                                        <span>{country || 'Not set'}</span>
                                    )}
                                </Col>
                            </li>
                        </ul>

                        {isEditing && (
                            <div className="account_card_list_btns mt-xxl-0 mt-3">
                                <button className="btn-style" onClick={handleSave}>
                                    Save
                                </button>
                                <button className="btn-style gray-btn" onClick={handleCancel}>
                                    Cancel
                                </button>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </Col>
    );
}
