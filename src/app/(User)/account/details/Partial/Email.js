'use client';
import React, { useState } from 'react';
import { Col } from 'react-bootstrap';
import { EditIconSvg } from '@/assets/svgIcons/SvgIcon';
import Link from 'next/link';
import { maskEmail } from '@/utils/emailMask';


export default function Email() {

    const email = "<EMAIL>";
    const maskedEmail = maskEmail(email);

    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Email</h6>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href="/security-check" prefetch={true}>
                                <button className="d-flex align-items-center">
                                    <EditIconSvg />
                                    <span className="ms-2">Update</span>
                                </button>
                            </Link>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <ul>
                                <li>
                                    <Col xs={12} md={3}>
                                        <span>Email </span>
                                    </Col>
                                    <Col xs={12} md={3}>
                                        <span>{maskedEmail}</span>
                                    </Col>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Col >
        </>
    )
}
