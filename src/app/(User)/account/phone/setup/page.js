'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import "@/css/account/AccountDetails.scss";

export default function SetupPhoneNumber() {
    const [phoneNumber, setPhoneNumber] = useState('');

    const handlePhoneChange = (e) => {
        const value = e.target.value;
        if (/^\+?\d*$/.test(value)) {
            setPhoneNumber(value);
        }
    };

    const metaArray = {
        noindex: true,
        title: "Setup Phone Number | Update Info | TradeReply",
        description: "Update your phone number on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Setup Phone Number | Update Info | TradeReply",
        og_description: "Update your phone number on TradeReply.com.",
        twitter_title: "Setup Phone Number | Update Info | TradeReply",
        twitter_description: "Update your phone number on TradeReply.com.",
    };
    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_phone_number">
                    <SidebarHeading title="New Phone Number" />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <h6>Phone Number</h6>
                                    <p>Provide your number to receive occasional updates, exclusive offers, or important notifications. We will never share your number without consent.</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12">
                                        <TextInput
                                            type="text"
                                            placeholder="Enter your phone number"
                                            value={phoneNumber}
                                            onChange={handlePhoneChange}
                                            inputMode="numeric"
                                        />
                                    </div>
                                    <div className="col-md-8 col-12">
                                        <label
                                            className="d-flex gap-3"
                                            htmlFor="sms-product"
                                        >
                                            <input
                                                className="custom_checkbox_input form-check-input"
                                                type="checkbox"
                                                id="sms-product"
                                            />
                                            <div>
                                                <h6>SMS Product Updates & Offers</h6>
                                                <p> Receive text messages with feature announcements, product updates, and occasional offers from TradeReply. By enabling this, you agree to recelive recuring marketing texts at the number provided. Consent is not a condition of purchase. Message and data rates may apply, Text STOP to opt out, or HELP for help,</p>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button className="btn-style white-btn" >
                                Cancel
                            </button>
                            <button className="btn-style" >
                                Save
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
