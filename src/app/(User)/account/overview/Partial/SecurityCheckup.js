'use client';
import { Col, Row } from "react-bootstrap";
import React from 'react'
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { CheckIcon, PlusIconSvg, RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import CircularProgressbarWithChildren from "@/Components/graph/CircularProgressbarWithChildren";
import Link from "next/link";

export default function SecurityCheckup() {
    const value = 67;

    return (
        <>
            <Col md={6} xs={12} className="mb-3 mb-lg-4">
                <CommonBlackCard
                    link="/account/security"
                    title="Security Checkup"
                    Linktext="Security"
                    Linkicon={<RightArrowIconSvg />}
                    className="account_card"
                >
                    <div className="account_card_checkup d-flex">
                        <div className="account_card_checkup_verify">
                            <div className="mb-3 d-flex align-items-center">
                                <CheckIcon />
                                <h6 className="ps-4">Email Verified</h6>
                            </div>
                            <div className="mb-3 d-flex align-items-center">
                                <CheckIcon />
                                <h6 className="ps-4">2-Step Verification</h6>
                            </div>
                            <div className="d-flex align-items-center">
                                <Link href="/security-check" prefetch={true}>
                                    <button className="add_number d-flex align-items-center" type="button">
                                        <PlusIconSvg /> Add phone number
                                    </button>
                                </Link>
                            </div>
                        </div>
                        <div className="account_card_checkup_chart">
                            <CircularProgressbarWithChildren value={value}>
                                <div className="CircularProgressbar_text">
                                    <h6>{value}%</h6>
                                    <h6 className="text-uppercase">Complete</h6>
                                </div>
                            </CircularProgressbarWithChildren>
                        </div>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    )
}
