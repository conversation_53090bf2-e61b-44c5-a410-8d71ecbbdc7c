'use client';
import React, { useState, useEffect } from 'react';
import { Col, Row } from 'react-bootstrap';
import { useSelector, useDispatch } from 'react-redux';
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import { get } from "@/utils/apiUtils";
import { setUser } from "@/redux/authSlice";
import Link from 'next/link';

export default function YourInformation() {
    const [userData, setUserData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Utility function to mask name
    const maskName = (name) => {
        if (!name || name.length === 0) return '';
        if (name.length === 1) return name;
        return name[0] + '*'.repeat(Math.max(name.length - 1, 3));
    };

    // Utility function to mask email
    const maskEmail = (email) => {
        if (!email) return '';
        const [localPart, domain] = email.split('@');
        if (!localPart || !domain) return email;

        const maskedLocal = localPart.length > 2
            ? localPart[0] + '*'.repeat(Math.max(localPart.length - 2, 8)) + localPart[localPart.length - 1]
            : localPart[0] + '*'.repeat(8);

        return `${maskedLocal}@${domain}`;
    };

    // Format phone number for display
    const formatPhoneNumber = (phone) => {
        if (!phone) return null;
        // Remove all non-digits
        const cleaned = phone.replace(/\D/g, '');
        // Format as (XXX) XXX-XXXX for US numbers, or just return as-is for others
        if (cleaned.length === 10) {
            return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
        }
        return phone;
    };

    // Fetch user data from API
    const fetchUserData = async () => {
        try {
            setLoading(true);
            setError(null);

            const response = await get('/account');

            if (response.success && response.data) {
                setUserData(response.data);
                // Update Redux store with fresh user data
                dispatch(setUser(response.data));
            } else {
                throw new Error(response.message || 'Failed to fetch user data');
            }
        } catch (err) {
            console.error('Error fetching user data:', err);
            setError(err.message || 'Failed to load user information');

            // Fallback to Redux user data if API fails
            if (reduxUser) {
                setUserData(reduxUser);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        // Use Redux data if available, otherwise fetch from API
        if (reduxUser) {
            setUserData(reduxUser);
            setLoading(false);
        } else {
            // Check localStorage for user data as fallback
            const storedUser = localStorage.getItem('user');
            if (storedUser) {
                try {
                    const parsedUser = JSON.parse(storedUser);
                    setUserData(parsedUser);
                    dispatch(setUser(parsedUser)); // Update Redux store
                    setLoading(false);
                } catch (err) {
                    console.error('Error parsing stored user data:', err);
                    fetchUserData();
                }
            } else {
                fetchUserData();
            }
        }
    }, [reduxUser, dispatch]);

    // Get display values with fallbacks
    const getDisplayName = () => {
        if (!userData) return 'Loading...';

        const firstName = userData.first_name || '';
        const lastName = userData.last_name || '';

        if (!firstName && !lastName) {
            // Fallback to full name if first/last names are not available
            const fullName = userData.name || '';
            if (fullName) {
                const nameParts = fullName.split(' ');
                return nameParts.map(part => maskName(part)).join(' ');
            }
            return 'Not set';
        }

        return `${maskName(firstName)} ${maskName(lastName)}`.trim();
    };

    const getDisplayEmail = () => {
        if (!userData) return 'Loading...';
        return userData.email ? maskEmail(userData.email) : 'Not set';
    };

    const getDisplayUsername = () => {
        if (!userData) return 'Loading...';
        return userData.username || 'Not set';
    };

    const getDisplayPhoneNumber = () => {
        if (!userData) return null;
        return userData.phone_number ? formatPhoneNumber(userData.phone_number) : null;
    };

    return (
        <>
            <Col md={6} xs={12} className="mb-3 mb-lg-4">
                <CommonBlackCard
                    link="/account/details"
                    title="Your Information"
                    Linktext="Account Details"
                    Linkicon={<RightArrowIconSvg />}
                    className="account_card"
                >
                    <div className="account_card_list">
                        {loading ? (
                            <div className="text-center py-3">
                                <span>Loading user information...</span>
                            </div>
                        ) : error ? (
                            <div className="text-center py-3">
                                <span className="text-danger">Failed to load user information</span>
                                <br />
                                <button
                                    className="btn btn-sm btn-link"
                                    onClick={fetchUserData}
                                    style={{ color: '#007bff', textDecoration: 'underline' }}
                                >
                                    Retry
                                </button>
                            </div>
                        ) : (
                            <ul>
                                <li>
                                    <span>Name </span> {getDisplayName()}
                                </li>
                                <li>
                                    <span>Email </span> {getDisplayEmail()}
                                </li>
                                <li>
                                    <span>Username </span> {getDisplayUsername()}
                                </li>
                                <li>
                                    <span>Phone Number </span>{" "}
                                    {getDisplayPhoneNumber() ? (
                                        <span>{getDisplayPhoneNumber()}</span>
                                    ) : (
                                        <Link href="/security-check" prefetch={true}>
                                            <button className="add_phone_number" type="button">
                                                Add Phone Number
                                            </button>
                                        </Link>
                                    )}
                                </li>
                            </ul>
                        )}
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    );
}
