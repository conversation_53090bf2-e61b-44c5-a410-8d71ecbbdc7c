'use client';
import React, { useState } from 'react';
import { Col, Row } from 'react-bootstrap';
import CommonBlackCard from "@/Components/common/Account/CommonBlackCard";
import { RightArrowIconSvg } from "@/assets/svgIcons/SvgIcon";
import Link from 'next/link';

export default function YourInformation() {

    return (
        <>
            <Col md={6} xs={12} className="mb-3 mb-lg-4">
                <CommonBlackCard
                    link="/account/details"
                    title="Your Information"
                    Linktext="Account Details"
                    Linkicon={<RightArrowIconSvg />}
                    className="account_card"
                >
                    <div className="account_card_list">
                        <ul>
                            <li>
                                <span>Name </span> A**** M****
                            </li>
                            <li>
                                <span>Email </span> a**********@yahooo.com
                            </li>
                            <li>
                                <span>Username </span> AaronMcCloud123
                            </li>
                            <li>
                                <span>Phone Number </span>{" "}
                                <Link href="/security-check" prefetch={true}>
                                    <button className="add_phone_number" type="button">
                                        Add Phone Number
                                    </button>
                                </Link>
                            </li>
                        </ul>
                    </div>
                </CommonBlackCard>
            </Col>
        </>
    );
}
