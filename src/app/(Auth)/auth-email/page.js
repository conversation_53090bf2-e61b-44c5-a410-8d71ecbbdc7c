import React from 'react'
import MetaHead from '@/Seo/Meta/MetaHead'
import Link from 'next/link'

export default function AuthEmail() {
    return (
        <>  <MetaHead title="Auth Email"></MetaHead>
            <div className='authEmailBg'>
                <div className="authEmailBg_authContainer">
                    <img src="https://cdn.tradereply.com/dev/site-assets/tradereply-trading-insights-logo.svg" alt="Brand Logo" />
                    <div className='authEmailBg_authContainer_inner'>
                        <p>Hello</p>
                        <p>You're initiated account recovery for your TradeReply Account. To reset your password, please enter thre security code below on our website:
                        </p>
                        <Link href="#">
                            <button>
                                RVELP8
                            </button>
                        </Link>
                        <p>
                            This code will expire in 15 minutes. Please use it promptly to complete your password reset.
                        </p>
                        <p>
                            If you did not initiate account recovery, please disregard this email.
                        </p>
                        <p>
                            Your account details are as follows:
                        </p>
                        <div className='authEmailBg_authContainer_inner_user'>
                            <p>Username: <span>aaron</span></p>
                            <p>Email: <span><EMAIL></span></p>
                        </div>
                        <p>
                            If you experience any issue during the account recovery process, feel free to contact us at
                            <a href="#" target='_blank'><EMAIL></a>, or reply to this email.
                        </p>
                        <p>Thank you,</p>
                        <p>The TradeReply Team</p>
                    </div>
                    <div className='authEmailBg_authContainer_outer'>
                        <p>For further visit or <a href="#" target='_blank'>Support Center</a></p>
                        <p>Copyright 2025 TradeReply. All Rights Reserved.</p>
                        <p>705 S Beach St Apt 146 | Daytons Beach, Ft 32114</p>
                    </div>
                </div>
            </div>
        </>
    )
}
